-- Client-side NPC vehicle spawning control
local npcVehiclesEnabled = true

-- Register the command using ox_lib
lib.addCommand('mistni-off', {
    help = 'Zapne/vypne spawning místních vozidel (pouze pro adminy)',
    restricted = false -- We'll handle admin check on server side
}, function(source, args, raw)
    -- Trigger server event to handle the command
    TriggerServerEvent('npc:toggleVehicleSpawning')
end)

-- Handle server response
RegisterNetEvent('npc:updateVehicleSpawning')
AddEventHandler('npc:updateVehicleSpawning', function(enabled)
    npcVehiclesEnabled = enabled
    
    if enabled then
        -- Enable NPC vehicle spawning
        SetVehicleDensityMultiplierThisFrame(1.0)
        SetPedDensityMultiplierThisFrame(1.0)
        SetRandomVehicleDensityMultiplierThisFrame(1.0)
        SetParkedVehicleDensityMultiplierThisFrame(1.0)
        SetScenarioPedDensityMultiplierThisFrame(1.0, 1.0)
        
        -- Notification
        lib.notify({
            title = 'Místní vozidla',
            description = 'Spawning místních vozidel byl zapnut',
            type = 'success',
            duration = 3000
        })
    else
        -- Disable NPC vehicle spawning
        SetVehicleDensityMultiplierThisFrame(0.0)
        SetPedDensityMultiplierThisFrame(0.0)
        SetRandomVehicleDensityMultiplierThisFrame(0.0)
        SetParkedVehicleDensityMultiplierThisFrame(0.0)
        SetScenarioPedDensityMultiplierThisFrame(0.0, 0.0)
        
        -- Notification
        lib.notify({
            title = 'Místní vozidla',
            description = 'Spawning místních vozidel byl vypnut',
            type = 'error',
            duration = 3000
        })
    end
end)

-- Handle permission denied
RegisterNetEvent('npc:permissionDenied')
AddEventHandler('npc:permissionDenied', function()
    lib.notify({
        title = 'Chyba',
        description = 'Nemáte oprávnění k použití tohoto příkazu',
        type = 'error',
        duration = 3000
    })
end)

-- Main thread to continuously apply the settings
CreateThread(function()
    while true do
        Wait(0)
        
        if not npcVehiclesEnabled then
            -- Continuously disable NPC spawning
            SetVehicleDensityMultiplierThisFrame(0.0)
            SetPedDensityMultiplierThisFrame(0.0)
            SetRandomVehicleDensityMultiplierThisFrame(0.0)
            SetParkedVehicleDensityMultiplierThisFrame(0.0)
            SetScenarioPedDensityMultiplierThisFrame(0.0, 0.0)
        else
            -- Ensure normal spawning
            SetVehicleDensityMultiplierThisFrame(1.0)
            SetPedDensityMultiplierThisFrame(1.0)
            SetRandomVehicleDensityMultiplierThisFrame(1.0)
            SetParkedVehicleDensityMultiplierThisFrame(1.0)
            SetScenarioPedDensityMultiplierThisFrame(1.0, 1.0)
        end
    end
end)
