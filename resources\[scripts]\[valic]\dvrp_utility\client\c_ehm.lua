-- local isProcessing = false

-- Citizen.CreateThread(function()
--     while true do
--         Citizen.Wait(0)

--         if IsControlJustPressed(0, 303) and not isProcessing then
--             isProcessing = true

--             local playerPed = PlayerPedId()
--             local playerId = PlayerId()
--             local playerName = GetPlayerName(playerId)
--             local playerCoords = GetEntityCoords(playerPed)

--             TriggerServerEvent('ehm:processRequest', {
--                 playerName = playerName,
--                 playerId = GetPlayerServerId(playerId),
--                 coords = playerCoords,
--                 timestamp = os.date('%Y-%m-%d %H:%M:%S')
--             })

--             Citizen.Wait(2000)
--             isProcessing = false
--         end
--     end
-- end)