-- local webhookUrl = "https://discord.com/api/webhooks/1402010652335411281/oGnbus1t-gJdzNWsd2tUDUvZH-GTd1UntC2LvaorIdD7C2EJGQVzbx7CcXc2AYx7vpbq"

-- function SendDiscordWebhook(data)
--     local embed = {
--         {
--             title = "🚨 PODEZŘENÍ NA CHEATY",
--             description = "Byl zaznamenán podez<PERSON>elý hráč",
--             color = 16711680,
--             fields = {
--                 {
--                     name = "👤 Hráč",
--                     value = data.playerName .. " (ID: " .. data.playerId .. ")",
--                     inline = true
--                 },
--                 {
--                     name = "📍 Pozice",
--                     value = string.format("X: %.2f, Y: %.2f, Z: %.2f", data.coords.x, data.coords.y, data.coords.z),
--                     inline = true
--                 },
--                 {
--                     name = "⏰ Čas",
--                     value = data.timestamp,
--                     inline = true
--                 },
--                 {
--                     name = "🔍 Důvod",
--                     value = "Manuální report - podez<PERSON>elé chován<PERSON>",
--                     inline = false
--                 }
--             },
--             image = {
--                 url = data.imageUrl
--             },
--             footer = {
--                 text = "DVRP System"
--             },
--             timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
--         }
--     }

--     local payload = {
--         username = "DVRP System",
--         embeds = embed
--     }

--     PerformHttpRequest(webhookUrl, function(err, text, headers)
--     end, 'POST', json.encode(payload), {
--         ['Content-Type'] = 'application/json'
--     })
-- end

-- RegisterServerEvent('ehm:processRequest')
-- AddEventHandler('ehm:processRequest', function(data)
--     local source = source

--     if data.playerId == source then
--         exports['screenshot-basic']:requestClientScreenshotUploadToDiscord(source, webhookUrl, {
--             encoding = 'jpg',
--             quality = 0.8
--         }, function(success, imageUrl)
--             if success and imageUrl then
--                 data.imageUrl = imageUrl
--                 SendDiscordWebhook(data)
--             end
--         end)
--     end
-- end)