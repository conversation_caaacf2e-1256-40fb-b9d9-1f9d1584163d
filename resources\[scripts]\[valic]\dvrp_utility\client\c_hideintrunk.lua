local inTrunk = false
local ox_target = exports.ox_target
local ox_lib = exports.ox_lib

local trunkTargets = {}
local trunkOpen = false

-- Track trunk zone handles per vehicle
local trunkZones = {}

function Draw3DText(x, y, z, text)
    local onScreen,_x,_y=World3dToScreen2d(x, y, z)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x,_y)
    local factor = (string.len(text)) / 370
    DrawRect(_x,_y+0.0125, 0.015+ factor, 0.03, 41, 11, 41, 68)
end

Citizen.CreateThread(function()
    while true do
        Wait(0)
        if inTrunk then
            local vehicle = GetEntityAttachedTo(PlayerPedId())
            if DoesEntityExist(vehicle) or not IsPedDeadOrDying(PlayerPedId()) or not IsPedFatallyInjured(PlayerPedId()) then
                local coords = GetWorldPositionOfEntityBone(vehicle, GetEntityBoneIndexByName(vehicle, 'boot'))
                SetEntityCollision(PlayerPedId(), false, false)
				DisableControlAction(0, 23, true)

                if GetVehicleDoorAngleRatio(vehicle, 5) < 0.9 then
                    SetEntityVisible(PlayerPedId(), false, false)
                else
                    if not IsEntityPlayingAnim(PlayerPedId(), 'timetable@floyd@cryingonbed@base', 3) then
                        loadDict('timetable@floyd@cryingonbed@base')
                        TaskPlayAnim(PlayerPedId(), 'timetable@floyd@cryingonbed@base', 'base', 8.0, -8.0, -1, 1, 0, false, false, false)
                        SetEntityVisible(PlayerPedId(), true, false)
                    end
                end

                -- Draw text for controls
                Draw3DText(coords.x, coords.y, coords.z + 0.5, "[E] Vylézt\n[G] Otevrít/Zavrít kufr")

                -- Get out of trunk with E key
                if IsControlJustPressed(0, 38) then -- E key
                    SetCarBootOpen(vehicle)
                    SetEntityCollision(PlayerPedId(), true, true)
                    -- Restore collision with all nearby vehicles
                    local playerCoords = GetEntityCoords(PlayerPedId())
                    for veh in EnumerateVehicles() do
                        if #(GetEntityCoords(veh) - playerCoords) < 20.0 then
                            SetEntityNoCollisionEntity(PlayerPedId(), veh, false)
                        end
                    end
                    SetEntityNoCollisionEntity(PlayerPedId(), vehicle, false)
                    Wait(750)
                    inTrunk = false
                    trunkOpen = false
                    DetachEntity(PlayerPedId(), true, true)
                    SetEntityVisible(PlayerPedId(), true, false)
                    ClearPedTasks(PlayerPedId())
                    SetEntityCoords(PlayerPedId(), GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, -0.5, -0.75))
                    Wait(250)
                    SetVehicleDoorShut(vehicle, 5)
                end

                -- Toggle trunk with G key
                if IsControlJustPressed(0, 47) then -- G key
                    if not trunkOpen then
                        SetCarBootOpen(vehicle)
                        trunkOpen = true
                    else
                        SetVehicleDoorShut(vehicle, 5)
                        trunkOpen = false
                    end
                end
            else
                SetEntityCollision(PlayerPedId(), true, true)
                -- Ensure collision with the car specifically
                if vehicle then
                    SetEntityNoCollisionEntity(PlayerPedId(), vehicle, false)
                end
                DetachEntity(PlayerPedId(), true, true)
                SetEntityVisible(PlayerPedId(), true, false)
                ClearPedTasks(PlayerPedId())
                SetEntityCoords(PlayerPedId(), GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, -0.5, -0.75))
                trunkOpen = false
            end
        end
    end
end)

Citizen.CreateThread(function()
    while not NetworkIsSessionStarted() do Wait(0) end
    while true do
        local playerPed = PlayerPedId()
        local vehicle = GetClosestVehicle(GetEntityCoords(playerPed), 10.0, 0, 70)
        if DoesEntityExist(vehicle) and IsVehicleSeatFree(vehicle, -1) then
            local trunkBoneName = 'boot'
            local trunkIndex = GetEntityBoneIndexByName(vehicle, trunkBoneName)
            if trunkIndex ~= -1 then
                local trunkCoords = GetWorldPositionOfEntityBone(vehicle, trunkIndex)
                if GetDistanceBetweenCoords(GetEntityCoords(playerPed), trunkCoords, true) <= 1.5 then
                    -- Always recreate the zone when near trunk
                    if trunkZones[vehicle] then
                        ox_target:removeZone(trunkZones[vehicle])
                        trunkZones[vehicle] = nil
                    end
                    trunkZones[vehicle] = ox_target:addBoxZone({
                        coords = trunkCoords,
                        entity = vehicle,
                        bone = trunkBoneName,
                        size = vec3(1.0, 1.0, 1.0),
                        rotation = 0,
                        debug = false,
                        options = {
                            {
                                name = 'hide_in_trunk',
                                label = 'Schovat se do kufru',
                                icon = 'fas fa-right-to-bracket',
                                onSelect = function(data)
                                    local vehicle = data.entity
                                    local playerPed = PlayerPedId()
                                    -- Only allow entry if vehicle is unlocked
                                    if GetVehicleDoorLockStatus(vehicle) ~= 1 then
                                        exports['okokNotify']:Alert('Kufr', 'Vozidlo je zamčené', 3000, 'error', true)
                                        return
                                    end
                                    if DoesEntityExist(playerPed) then
                                        if not IsEntityAttached(playerPed) or GetDistanceBetweenCoords(GetEntityCoords(playerPed), GetEntityCoords(vehicle), true) >= 5.0 then
                                            SetCarBootOpen(vehicle)
                                            Wait(350)
                                            AttachEntityToEntity(playerPed, vehicle, -1, 0.0, -2.2, 0.5, 0.0, 0.0, 0.0, false, false, false, false, 20, true)
                                            loadDict('timetable@floyd@cryingonbed@base')
                                            TaskPlayAnim(playerPed, 'timetable@floyd@cryingonbed@base', 'base', 8.0, -8.0, -1, 1, 0, false, false, false)
                                            Wait(50)
                                            inTrunk = true
                                            Wait(1500)
                                            SetVehicleDoorShut(vehicle, 5)
                                        else
                                            exports['okokNotify']:Alert('Kufr', 'Někdo už se v kufru nachází', 3000, 'error', true)
                                        end
                                    end
                                end
                            }
                        }
                    })
                else
                    -- Remove zone if player leaves area
                    if trunkZones[vehicle] then
                        ox_target:removeZone(trunkZones[vehicle])
                        trunkZones[vehicle] = nil
                    end
                end
            end
        end
        Wait(500)
    end
end)

-- Load animation dictionary (defined only once now)
function loadDict(dict)
    while not HasAnimDictLoaded(dict) do
        RequestAnimDict(dict)
        Wait(0)
    end
end

-- Utility to enumerate vehicles
function EnumerateVehicles()
    return coroutine.wrap(function()
        local handle, veh = FindFirstVehicle()
        if not handle or handle == -1 then return end
        local finished = false
        repeat
            coroutine.yield(veh)
            finished, veh = FindNextVehicle(handle)
        until not finished
        EndFindVehicle(handle)
    end)
end

