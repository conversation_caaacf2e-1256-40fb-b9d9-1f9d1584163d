-- Server-side NPC vehicle spawning control
local npcVehiclesEnabled = true

-- Function to check if player is admin
local function isPlayerAdmin(source)
    -- Check if player has admin ace permission
    if IsPlayerAceAllowed(source, "command") then
        return true
    end
    
    -- Alternative: Check for specific admin ace
    if IsPlayer<PERSON>ceAllowed(source, "admin") then
        return true
    end
    
    -- Alternative: Check for group admin
    if IsPlayerAceAllowed(source, "group.admin") then
        return true
    end
    
    -- You can also add specific identifiers here if needed
    local identifiers = GetPlayerIdentifiers(source)
    for _, identifier in pairs(identifiers) do
        -- Example: Add specific Steam IDs that should have admin access
        -- if identifier == "steam:110000xxxxxxxxx" then
        --     return true
        -- end
    end
    
    return false
end

-- Handle the toggle command
RegisterServerEvent('npc:toggleVehicleSpawning')
AddEventHandler('npc:toggleVehicleSpawning', function()
    local source = source
    
    -- Check if player is admin
    if not isPlayerAdmin(source) then
        TriggerClientEvent('npc:permissionDenied', source)
        return
    end
    
    -- Toggle the state
    npcVehiclesEnabled = not npcVehiclesEnabled
    
    -- Send update to all clients
    TriggerClientEvent('npc:updateVehicleSpawning', -1, npcVehiclesEnabled)
    
    -- Log the action
    local playerName = GetPlayerName(source)
    local status = npcVehiclesEnabled and "zapnut" or "vypnut"
    print(string.format("[NPC Control] %s (%d) %s spawning místních vozidel", playerName, source, status))
end)

-- Command to check current status (for admins)
RegisterCommand('mistni-status', function(source, args, rawCommand)
    if source == 0 then
        -- Console command
        local status = npcVehiclesEnabled and "zapnut" or "vypnut"
        print(string.format("[NPC Control] Spawning místních vozidel je aktuálně: %s", status))
        return
    end
    
    -- Check if player is admin
    if not isPlayerAdmin(source) then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Chyba]", "Nemáte oprávnění k použití tohoto příkazu"}
        })
        return
    end
    
    local status = npcVehiclesEnabled and "zapnut" or "vypnut"
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"[NPC Control]", string.format("Spawning místních vozidel je aktuálně: %s", status)}
    })
end, false)

-- Send current state to player when they join
AddEventHandler('playerJoining', function()
    local source = source
    -- Wait a bit for the player to fully load
    SetTimeout(5000, function()
        TriggerClientEvent('npc:updateVehicleSpawning', source, npcVehiclesEnabled)
    end)
end)

-- Alternative event for when player spawns (if using ESX/QBCore)
RegisterNetEvent('QBCore:Server:PlayerLoaded')
AddEventHandler('QBCore:Server:PlayerLoaded', function(source)
    TriggerClientEvent('npc:updateVehicleSpawning', source, npcVehiclesEnabled)
end)

-- Alternative for ESX
RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(source)
    TriggerClientEvent('npc:updateVehicleSpawning', source, npcVehiclesEnabled)
end)
